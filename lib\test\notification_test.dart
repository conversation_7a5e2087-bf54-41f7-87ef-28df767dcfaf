import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import '../main.dart';

class NotificationTestScreen extends StatefulWidget {
  const NotificationTestScreen({Key? key}) : super(key: key);

  @override
  State<NotificationTestScreen> createState() => _NotificationTestScreenState();
}

class _NotificationTestScreenState extends State<NotificationTestScreen> {
  String _testResults = "";

  void _addTestResult(String result) {
    setState(() {
      _testResults += "$result\n";
    });
    print(result);
  }

  Future<void> _testNotificationPermissions() async {
    _addTestResult("🔐 [TEST] Testing notification permissions...");
    
    final AndroidFlutterLocalNotificationsPlugin? androidImplementation =
        flutterLocalNotificationsPlugin.resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>();

    if (androidImplementation != null) {
      final bool? areNotificationsEnabled = await androidImplementation.areNotificationsEnabled();
      _addTestResult("📱 [TEST] Notifications enabled: $areNotificationsEnabled");
      
      if (areNotificationsEnabled == false) {
        _addTestResult("❌ [TEST] Notifications are disabled - requesting permission");
        final bool? granted = await androidImplementation.requestNotificationsPermission();
        _addTestResult("🔐 [TEST] Permission granted: $granted");
      }
    } else {
      _addTestResult("❌ [TEST] Could not get Android implementation");
    }
  }

  Future<void> _testBasicNotification() async {
    _addTestResult("🔔 [TEST] Testing basic notification...");
    
    try {
      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        'test_channel_id',
        'Test Notifications',
        channelDescription: 'Test notifications',
        importance: Importance.max,
        priority: Priority.high,
      );

      const NotificationDetails platformChannelSpecifics =
          NotificationDetails(android: androidPlatformChannelSpecifics);

      await flutterLocalNotificationsPlugin.show(
        1,
        'Test Notification',
        'This is a test notification',
        platformChannelSpecifics,
      );
      
      _addTestResult("✅ [TEST] Basic notification sent successfully");
    } catch (e) {
      _addTestResult("❌ [TEST] Basic notification failed: $e");
    }
  }

  Future<void> _testCallNotification() async {
    _addTestResult("📞 [TEST] Testing call notification...");
    
    try {
      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        'call_channel_id',
        'Call Notifications',
        channelDescription: 'Notifications for incoming calls',
        importance: Importance.max,
        priority: Priority.high,
        ongoing: true,
        autoCancel: false,
        fullScreenIntent: true,
        category: AndroidNotificationCategory.call,
        visibility: NotificationVisibility.public,
        showWhen: true,
        timeoutAfter: 30000,
        actions: <AndroidNotificationAction>[
          AndroidNotificationAction(
            showsUserInterface: true,
            'answer',
            'Answer',
          ),
          AndroidNotificationAction(
            showsUserInterface: true,
            'reject',
            'Reject',
          ),
        ],
      );

      const NotificationDetails platformChannelSpecifics =
          NotificationDetails(android: androidPlatformChannelSpecifics);

      await flutterLocalNotificationsPlugin.show(
        0,
        'Test Incoming Call',
        'Test Caller (1234567890)',
        platformChannelSpecifics,
        payload: '1234567890|Test Caller',
      );
      
      _addTestResult("✅ [TEST] Call notification sent successfully");
    } catch (e) {
      _addTestResult("❌ [TEST] Call notification failed: $e");
    }
  }

  Future<void> _clearNotifications() async {
    _addTestResult("🧹 [TEST] Clearing all notifications...");
    await flutterLocalNotificationsPlugin.cancelAll();
    _addTestResult("✅ [TEST] All notifications cleared");
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notification Test'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Notification System Test',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            
            ElevatedButton(
              onPressed: _testNotificationPermissions,
              child: const Text('Test Permissions'),
            ),
            const SizedBox(height: 10),
            
            ElevatedButton(
              onPressed: _testBasicNotification,
              child: const Text('Test Basic Notification'),
            ),
            const SizedBox(height: 10),
            
            ElevatedButton(
              onPressed: _testCallNotification,
              child: const Text('Test Call Notification'),
            ),
            const SizedBox(height: 10),
            
            ElevatedButton(
              onPressed: _clearNotifications,
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              child: const Text('Clear All Notifications'),
            ),
            const SizedBox(height: 10),
            
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _testResults = "";
                });
              },
              style: ElevatedButton.styleFrom(backgroundColor: Colors.grey),
              child: const Text('Clear Results'),
            ),
            const SizedBox(height: 20),
            
            const Text(
              'Test Results:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _testResults.isEmpty ? "No test results yet..." : _testResults,
                    style: const TextStyle(fontFamily: 'monospace'),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
