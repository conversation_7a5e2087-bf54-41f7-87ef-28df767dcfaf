import 'dart:async';
import 'dart:io';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:flutter_background_service_android/flutter_background_service_android.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../sip_account/SIPConfiguration.dart';

class BackgroundCallService {
  static const String _serviceId = 'call_background_service';

  static Future<void> initializeService() async {
    print("🔧 [BACKGROUND_SERVICE] Initializing background service");

    final service = FlutterBackgroundService();

    // Configure the notification for the background service
    const AndroidNotificationChannel channel = AndroidNotificationChannel(
      'background_service_channel',
      'Background Call Service',
      description: 'This channel is used for background call handling',
      importance: Importance.low,
    );

    final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
        FlutterLocalNotificationsPlugin();

    if (Platform.isAndroid) {
      await flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>()
          ?.createNotificationChannel(channel);
    }

    await service.configure(
      androidConfiguration: AndroidConfiguration(
        onStart: onStart,
        autoStart: true,
        isForegroundMode: true,
        notificationChannelId: 'background_service_channel',
        initialNotificationTitle: 'Call Service',
        initialNotificationContent: 'Ready to receive calls',
        foregroundServiceNotificationId: 888,
      ),
      iosConfiguration: IosConfiguration(
        autoStart: true,
        onForeground: onStart,
        onBackground: onIosBackground,
      ),
    );

    print("✅ [BACKGROUND_SERVICE] Background service configured");
  }

  @pragma('vm:entry-point')
  static void onStart(ServiceInstance service) async {
    print("🚀 [BACKGROUND_SERVICE] Service started");

    DartPluginRegistrant.ensureInitialized();

    final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
        FlutterLocalNotificationsPlugin();

    if (service is AndroidServiceInstance) {
      service.on('setAsForeground').listen((event) {
        service.setAsForegroundService();
      });

      service.on('setAsBackground').listen((event) {
        service.setAsBackgroundService();
      });
    }

    service.on('stopService').listen((event) {
      service.stopSelf();
    });

    // Keep the service alive and monitor for incoming calls
    Timer.periodic(const Duration(seconds: 10), (timer) async {
      if (service is AndroidServiceInstance) {
        if (await service.isForegroundService()) {
          // Update notification to show service is active
          flutterLocalNotificationsPlugin.show(
            888,
            'Call Service Active',
            'Monitoring for incoming calls...',
            const NotificationDetails(
              android: AndroidNotificationDetails(
                'background_service_channel',
                'Background Call Service',
                icon: 'ic_bg_service_small',
                ongoing: true,
              ),
            ),
          );
        }
      }

      // Check if we need to stop the service
      final prefs = await SharedPreferences.getInstance();
      final shouldStop = prefs.getBool('stop_background_service') ?? false;
      if (shouldStop) {
        timer.cancel();
        service.stopSelf();
      }
    });
  }

  @pragma('vm:entry-point')
  static Future<bool> onIosBackground(ServiceInstance service) async {
    print("📱 [BACKGROUND_SERVICE] iOS background mode");
    WidgetsFlutterBinding.ensureInitialized();
    DartPluginRegistrant.ensureInitialized();
    return true;
  }

  static Future<void> startService() async {
    print("▶️ [BACKGROUND_SERVICE] Starting service");
    final service = FlutterBackgroundService();
    var isRunning = await service.isRunning();
    if (!isRunning) {
      service.startService();
      print("✅ [BACKGROUND_SERVICE] Service started successfully");
    } else {
      print("ℹ️ [BACKGROUND_SERVICE] Service already running");
    }
  }

  static Future<void> stopService() async {
    print("⏹️ [BACKGROUND_SERVICE] Stopping service");
    final service = FlutterBackgroundService();
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('stop_background_service', true);
    service.invoke("stopService");
    print("✅ [BACKGROUND_SERVICE] Service stopped");
  }

  static Future<bool> isServiceRunning() async {
    final service = FlutterBackgroundService();
    return await service.isRunning();
  }
}
