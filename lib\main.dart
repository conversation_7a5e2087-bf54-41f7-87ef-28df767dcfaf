/// good working

// import 'dart:io';
// import 'package:animated_splash_screen/animated_splash_screen.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_local_notifications/flutter_local_notifications.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:get/get_navigation/get_navigation.dart';
// import 'package:get/get_navigation/src/root/get_material_app.dart';
// import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';
// import 'api/MyHttpOverrides.dart';
// import 'dashboard/Dashboard.dart';
// import 'sip_account/SIPConfiguration.dart';
//
// /*  LETEST DESIGN
//
//   Activity name : Main activity
//   Project name : iSalesCRM Mobile App
//
// */
// //Launch Screen
//
// /// new
// final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
// final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
//     FlutterLocalNotificationsPlugin();
//
// void main() async {
//   navigatorKey;
//   WidgetsFlutterBinding.ensureInitialized();
//
//   const AndroidInitializationSettings initializationSettingsAndroid =
//       AndroidInitializationSettings('@mipmap/ic_launcher');
//
//   final InitializationSettings initializationSettings = InitializationSettings(
//     android: initializationSettingsAndroid,
//   );
//
//   await flutterLocalNotificationsPlugin.initialize(
//     initializationSettings,
//     onDidReceiveNotificationResponse: (NotificationResponse response) {
//       if (response.payload != null) {
//         final List<String> payloadData = response.payload!.split('|');
//         if (payloadData.length == 2) {
//           String phoneNumber = payloadData[0];
//           String callerName = payloadData[1];
//           // Ensure handling only on user action
//           if (response.actionId == 'answer') {
//             SIPConfiguration.handleNotificationAction(
//               'answer',
//               navigatorKey.currentContext!,
//               phoneNumber,
//               callerName,
//             );
//           } else if (response.actionId == 'reject') {
//             SIPConfiguration.handleNotificationAction(
//               'reject',
//               navigatorKey.currentContext!,
//               phoneNumber,
//               callerName,
//             );
//           }
//         }
//       }
//     },
//   );
//
//   HttpOverrides.global = MyHttpOverrides();
//   runApp(const MyApp());
// }
//
// class MyApp extends StatelessWidget {
//   const MyApp({super.key});
//
//   // This widget is the root of your application.
//   @override
//   Widget build(BuildContext context){
//     return MaterialApp(
//       navigatorKey: navigatorKey,
//       debugShowCheckedModeBanner: false,
//       //theme: ThemeData.light(),
//       home: SplashScreen(),
//     );
//   }
// }
//
// // SplashScreen
// class SplashScreen extends StatefulWidget {
//   const SplashScreen({super.key});
//
//   @override
//   State<SplashScreen> createState() => _SplashScreenState();
// }
//
// class _SplashScreenState extends State<SplashScreen> {
//   bool loginStatus = false;
//   InternetConnectionCheckerPlus internetConnectionCheckerPlus =
//       InternetConnectionCheckerPlus();
//
//   @override
//   initState() {
//     // TODO: implement initState
//     super.initState();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return ScreenUtilInit(
//       child: GetMaterialApp(
//         debugShowCheckedModeBanner: false,
//         home: animatedIconShow(),
//       ),
//     );
//   }
//
// //Show animated splash screen then redirect to User First Screen
//   animatedIconShow() {
//     return AnimatedSplashScreen(
//         splash: Image.asset("assets/images/logo.png"),
//         splashTransition: SplashTransition.scaleTransition,
//         duration: 3000,
//         nextScreen: const DashboardScreen());
//   }
// }

///

import 'dart:io';

import 'package:dialercall/sip_account/SIPDialPad.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'api/MyHttpOverrides.dart';

import 'sip_account/SIPConfiguration.dart';

/*  LETEST DESIGN

  Activity name : Main activity
  Project name : iSalesCRM Mobile App

*/
//Launch Screen

/// new
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
    FlutterLocalNotificationsPlugin();

void main() async {
  navigatorKey;
  WidgetsFlutterBinding.ensureInitialized();

  const AndroidInitializationSettings initializationSettingsAndroid =
      AndroidInitializationSettings('@mipmap/ic_launcher');

  final InitializationSettings initializationSettings = InitializationSettings(
    android: initializationSettingsAndroid,
  );

  await flutterLocalNotificationsPlugin.initialize(
    initializationSettings,
    onDidReceiveNotificationResponse: (NotificationResponse response) {
      if (response.payload != null) {
        final List<String> payloadData = response.payload!.split('|');
        if (payloadData.length == 2) {
          String phoneNumber = payloadData[0];
          String callerName = payloadData[1];
          // Ensure handling only on user action
          if (response.actionId == 'answer') {
            SIPConfiguration.handleNotificationAction(
              'answer',
              navigatorKey.currentContext!,
              phoneNumber,
              callerName,
            );
          } else if (response.actionId == 'reject') {
            SIPConfiguration.handleNotificationAction(
              'reject',
              navigatorKey.currentContext!,
              phoneNumber,
              callerName,
            );
          }
        }
      }
    },
  );

  HttpOverrides.global = MyHttpOverrides();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(360, 690),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (_, child) {
        return MaterialApp(
          navigatorKey: navigatorKey,
          debugShowCheckedModeBanner: false,
          theme: ThemeData(
            primarySwatch: Colors.blue,
            visualDensity: VisualDensity.adaptivePlatformDensity,
          ),
          home: const SplashScreen(),
        );
      },
    );
  }
}

/// new splash screen good work
///
class SplashScreen extends StatefulWidget {
  const SplashScreen({Key? key}) : super(key: key);

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;
  late Animation<double> _loadingTextOpacity;
  String _loadingText = "Getting everything ready...";
  void checkSipAccountStatus() async {
    try {
      var ref = await SharedPreferences.getInstance();

      String? sipID = ref.getString("sipID");
      String? sipDomain = ref.getString("sipDomain");
      String? sipPassword = ref.getString("sipPassword");

      if (sipID != null && sipDomain != null && sipPassword != null) {
        SIPConfiguration.config(sipID, sipDomain, sipPassword, true, context);
        // SIPConfiguration.config(sipID, sipDomain, sipPassword, true, context);
      }
    } catch (e) {
      print("Error checking SIP account status: $e");
    }
  }

  @override
  void initState() {
    super.initState();
    checkSipAccountStatus();
    // Initialize animations
    _controller = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 0.5, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeOutBack,
      ),
    );

    _opacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeIn,
      ),
    );

    _loadingTextOpacity = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.8, 1.0),
      ),
    );

    // Start animation and initialize
    _controller.forward();
    _initialize();

    // Setup loading text animation
    _animateLoadingText();
  }

  void _animateLoadingText() {
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _loadingText = "Please wait...";
        });
        Future.delayed(const Duration(seconds: 1), () {
          if (mounted) {
            setState(() {
              _loadingText = "Getting everything ready...";
            });
            _animateLoadingText();
          }
        });
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> _initialize() async {
    await _requestPermissions();
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        Navigator.pushReplacement(
          context,
          PageRouteBuilder(
            pageBuilder: (context, animation, secondaryAnimation) =>
                const SipDialPad(
              phoneNumber: "",
              callerName: "",
            ),
            transitionsBuilder:
                (context, animation, secondaryAnimation, child) {
              return FadeTransition(
                opacity: animation,
                child: child,
              );
            },
            transitionDuration: const Duration(seconds: 1),
          ),
        );
      }
    });
  }

  Future<void> _requestPermissions() async {
    await Permission.microphone.request();
    await Permission.phone.request();
    await Permission.contacts.request();
    await Permission.storage.request();
    await Permission.systemAlertWindow.request();
    await Permission.ignoreBatteryOptimizations.request();
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.blue[400]!,
              Colors.blue[800]!,
            ],
          ),
        ),
        child: SafeArea(
          child: Stack(
            children: [
              // Background design elements
              Positioned(
                top: screenHeight * 0.1,
                right: -screenWidth * 0.2,
                child: Transform.rotate(
                  angle: -0.2,
                  child: Container(
                    width: screenWidth * 0.7,
                    height: screenWidth * 0.7,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(30),
                      color: Colors.white.withOpacity(0.1),
                    ),
                  ),
                ),
              ),
              Positioned(
                bottom: -screenHeight * 0.1,
                left: -screenWidth * 0.3,
                child: Container(
                  width: screenWidth * 0.8,
                  height: screenWidth * 0.8,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withOpacity(0.1),
                  ),
                ),
              ),

              // Main content
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Animated logo
                    AnimatedBuilder(
                      animation: _controller,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _scaleAnimation.value,
                          child: Opacity(
                            opacity: _opacityAnimation.value,
                            child: Container(
                              padding: EdgeInsets.all(screenWidth * 0.05),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                shape: BoxShape.circle,
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.2),
                                    blurRadius: 20,
                                    offset: const Offset(0, 10),
                                  ),
                                ],
                              ),
                              child: Image.asset(
                                "assets/images/logo.png",
                                width: screenWidth * 0.35,
                                height: screenWidth * 0.35,
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                    SizedBox(height: screenHeight * 0.06),

                    // Animated loading text
                    AnimatedOpacity(
                      opacity: 1.0,
                      duration: const Duration(seconds: 1),
                      child: Text(
                        _loadingText,
                        style: TextStyle(
                          fontSize: screenWidth * 0.045,
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                          letterSpacing: 1.2,
                        ),
                      ),
                    ),
                    SizedBox(height: screenHeight * 0.04),

                    // Enhanced loading indicator with pulse animation
                    TweenAnimationBuilder(
                      tween: Tween<double>(begin: 0.8, end: 1.2),
                      duration: const Duration(milliseconds: 1000),
                      builder: (context, double value, child) {
                        return Transform.scale(
                          scale: value,
                          child: SizedBox(
                            width: screenWidth * 0.12,
                            height: screenWidth * 0.12,
                            child: CircularProgressIndicator(
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.white),
                              strokeWidth: 4,
                              backgroundColor: Colors.white.withOpacity(0.2),
                            ),
                          ),
                        );
                      },
                      onEnd: () => setState(() {}), // Restart the animation
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// SplashScreen
//
// class SplashScreen extends StatefulWidget {
//   const SplashScreen({super.key});
//
//   @override
//   State<SplashScreen> createState() => _SplashScreenState();
// }
//
// class _SplashScreenState extends State<SplashScreen> {
//   bool loginStatus = false;
//   InternetConnectionCheckerPlus internetConnectionCheckerPlus =
//   InternetConnectionCheckerPlus();
//
//   @override
//   initState() {
//     // TODO: implement initState
//     super.initState();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return ScreenUtilInit(
//       child: GetMaterialApp(
//         debugShowCheckedModeBanner: false,
//         home: animatedIconShow(),
//       ),
//     );
//   }
//
// //Show animated splash screen then redirect to User First Screen
//   animatedIconShow() {
//     return AnimatedSplashScreen(
//         splash: Image.asset("assets/images/logo.png"),
//         splashTransition: SplashTransition.scaleTransition,
//         duration: 3000,
//         nextScreen: const DashboardScreen());
//   }
// }
