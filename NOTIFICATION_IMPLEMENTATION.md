# Call Notification Implementation

## Overview
This implementation adds proper call notifications for when the app is in the background. The system includes comprehensive logging to help debug notification issues.

## Changes Made

### 1. Android Manifest Updates (`android/app/src/main/AndroidManifest.xml`)
Added essential permissions for notifications:
- `POST_NOTIFICATIONS` - For Android 13+ notification permission
- `ACCESS_NOTIFICATION_POLICY` - For notification policy access
- `BIND_NOTIFICATION_LISTENER_SERVICE` - For notification listener service
- `DISABLE_KEYGUARD` - To show notifications on lock screen
- `TURN_SCREEN_ON` - To turn screen on for incoming calls
- `SHOW_WHEN_LOCKED` - To show notifications when device is locked

### 2. Main App Initialization (`lib/main.dart`)
- Added proper notification channel creation
- Enhanced permission handling including notification permissions
- Added comprehensive logging throughout the initialization process
- Integrated background service initialization

### 3. SIP Configuration Updates (`lib/sip_account/SIPConfiguration.dart`)
- Enhanced incoming call handling with better logging
- Improved notification system with error handling
- Added background service startup when SIP registration is successful
- Better notification configuration with sound, vibration, and LED

### 4. Background Service (`lib/services/background_call_service.dart`)
- Created a background service to handle calls when app is not active
- Ensures the app can receive and process incoming calls in background
- Maintains a foreground service for reliable call handling

### 5. Notification Test Screen (`lib/test/notification_test.dart`)
- Added a comprehensive test screen to verify notification functionality
- Tests basic notifications, call notifications, and permissions
- Accessible through the drawer menu

## How to Test

### 1. Build and Install the App
```bash
flutter clean
flutter pub get
flutter build apk --debug
# Install the APK on your device
```

### 2. Grant Permissions
When you first run the app, it will request several permissions:
- Microphone
- Phone
- Contacts
- Storage
- System Alert Window
- Battery Optimization
- Notifications (Android 13+)

**Important**: Make sure to grant all permissions, especially the notification permission.

### 3. Configure SIP Account
- Set up your SIP account in the app
- Ensure successful registration (you should see logs indicating background service started)

### 4. Test Notifications
#### Option A: Use the Test Screen
1. Open the drawer menu (hamburger icon)
2. Tap "Test Notifications"
3. Run through all the tests:
   - Test Permissions
   - Test Basic Notification
   - Test Call Notification

#### Option B: Test with Real Calls
1. Put the app in the background (press home button)
2. Make a call to your SIP number from another device
3. You should see a call notification with Answer/Reject buttons

### 5. Check Logs
Use `flutter logs` or `adb logcat` to see detailed logging:

```bash
# Flutter logs
flutter logs

# Or Android logs with filtering
adb logcat | grep -E "(MAIN|SIP|NOTIFICATION|BACKGROUND_SERVICE|PERMISSIONS)"
```

## Log Messages to Look For

### Successful Flow:
```
🚀 [MAIN] App initialization started
📢 [MAIN] Creating notification channel
✅ [MAIN] Call notification channel created successfully
🔐 [MAIN] Notification permission granted: true
🔧 [MAIN] Initializing background service
✅ [MAIN] Background service initialized
🔐 [PERMISSIONS] Requesting permissions
✅ [PERMISSIONS] All permissions requested
🔧 [SIP] Starting background service after successful registration
✅ [SIP] Background service started successfully
📞 [SIP] Ring event received: {callType: inbound, phoneNumber: 1234567890, callerName: Test Caller}
🔔 [SIP] Showing persistent notification
✅ [SIP] Persistent notification shown
```

### Common Issues and Solutions:

#### 1. Notifications Not Appearing
**Logs to check:**
```
❌ [NOTIFICATION] Notifications are disabled by user
❌ [NOTIFICATION] Failed to show notification: [error]
```

**Solutions:**
- Check device notification settings
- Ensure notification permission is granted
- Test with the notification test screen

#### 2. Background Service Not Starting
**Logs to check:**
```
❌ [SIP] Failed to start background service: [error]
❌ [BACKGROUND_SERVICE] Service failed to start
```

**Solutions:**
- Check battery optimization settings
- Ensure all permissions are granted
- Restart the app

#### 3. SIP Registration Issues
**Logs to check:**
```
📞 [SIP] SIP registration failed
❌ [SIP] Failed to configure SIP
```

**Solutions:**
- Check SIP credentials
- Verify network connectivity
- Check SIP server status

## Device-Specific Settings

### Samsung Devices
1. Go to Settings > Apps > [Your App] > Battery
2. Set to "Unrestricted"
3. Go to Settings > Apps > [Your App] > Notifications
4. Enable all notification categories

### Xiaomi/MIUI Devices
1. Go to Settings > Apps > Manage apps > [Your App]
2. Enable "Autostart"
3. Set Battery saver to "No restrictions"
4. Enable all notification permissions

### OnePlus/OxygenOS Devices
1. Go to Settings > Apps & notifications > [Your App]
2. Enable "Allow background activity"
3. Set Battery optimization to "Don't optimize"

## Troubleshooting

### If notifications still don't work:
1. Check the test screen results
2. Verify all permissions are granted
3. Check device-specific battery optimization settings
4. Ensure the app is not being killed by the system
5. Test on different devices/Android versions

### Debug Commands:
```bash
# Check if background service is running
adb shell dumpsys activity services | grep -i background

# Check notification channels
adb shell dumpsys notification | grep -A 5 "call_channel_id"

# Check app permissions
adb shell dumpsys package [your.package.name] | grep permission
```

## Next Steps
- Test on various Android versions and devices
- Monitor battery usage impact
- Consider implementing iOS CallKit for iOS devices
- Add more comprehensive error handling
- Implement notification sound customization
